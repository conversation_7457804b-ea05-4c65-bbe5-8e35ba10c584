import json
import os
from uuid import UUID
from .migration_service import MigrationService
from .process_files import process_files_for_migration
from app.core import logger
from app.services import (
     decode_secure_jwt,
     decode_base
)
from app.schemas.migrate import MigarationInput
from app.core import (
    Service<PERSON><PERSON>ponse,
    LOCAL_DOWNLOAD_PATH,
    S3_INPUT_PATH,
    LOCAL_WORKBOOKS_DOWNLOAD_PATH,
    S3_WOR<PERSON>BOOKS_PATH,
    HTTP_STATUS_OK,
    LOCAL_S3_FILES_DOWNLOAD_PATH,
    WOR<PERSON><PERSON>OKS_PATH,
    MIGRATE_OUTPUT_DIR,
    MIGRATE_S3_ZIP_PATH,
    POWER_BI_STRUCTURE
)
from app.core.config import S3Config
from app.core.exceptions import *
from pathlib import Path
from typing import Optional, List
import shutil
from starlette.concurrency import run_in_threadpool
from fastapi import HTTPException

# Import both old and new models
from app.models_old.reports import ReportDetailsManager as OldReportDetailsManager
from app.models_old.upload_file_report_details import UploadFilesReportManager
from app.models.report_details import ReportDetailsManager
from app.models.users import User
from app.services.analysis.s3_service import download_twb_files_from_s3_by_id


def parse_authorization_token(
        authorization: str,
        email: str
):
    """
    Parses the authorization token and returns
    the decoded email and JWT payload.

    Parameters
    ----------
    authorization : str
        Authorization header in the format "Bearer <token>".
    email : str
        User's email used for decoding the token.

    Returns
    -------
    A tuple containing the decoded email and the token payload.
    """
    try:
        token = authorization.split(" ")[1]
        decoded_email = decode_base(email)
        payload = decode_secure_jwt(token, decoded_email)
        return decoded_email, payload
    except Exception as e:
        logger.error(f"Error parsing authorization token: {e}")
        raise ValueError("Invalid authorization token")


class MigrateProcessor:
    """ All migration operations processed here"""

    @staticmethod
    async def migrate_tableau_to_powerbi_processor(report_id: UUID, user: User) -> ServiceResponse:
        """
        Processes Tableau to PowerBI migration request for a given report ID.
        - If already migrated, return pre-signed URL.
        - If not, perform migration and then return pre-signed URL.
        - Also includes hierarchical project path.

        Args:
            report_id (UUID): ID of the report to migrate.
            user: Logged-in user performing the action.

        Returns:
            ServiceResponse: with download URL and project/report path structure.
        """
        try:
            response = await MigrationService().migrate_tableau_to_powerbi_report(report_id, user)
            return ServiceResponse(
                data=response,
                error=None,
                status_code=200
            )
        except Exception as e:
            return ServiceResponse(
                data=None,
                error=str(e),
                status_code=500
            )

    @staticmethod
    async def tableau_to_powerbi_legacy(
         authorization: str,
         email: str,
         process_id: str,
    ) -> ServiceResponse:
        """ Migration of tableau files to powerBI """

        s3config = S3Config()
        logger.debug(f"/tableau-to-powerbi initiated for process_id: {process_id}, authorization: {authorization}, email: {email}")
        decoded_email, payload = parse_authorization_token(
             authorization,
             email
        )
        organization_name = payload.get('organizationName')
        user_email = payload.get('email')
        local_download_path = LOCAL_DOWNLOAD_PATH.format(
             organization_name=organization_name,
             user_email=user_email,
             process_id=process_id
        )
        os.makedirs(local_download_path,exist_ok=True)
        s3_input_path = S3_INPUT_PATH.format(
             organization_name = organization_name, 
             user_email = user_email,
             process_id = process_id
        )
        twb_files = await s3config.get_twb_files(
             s3_input_path,
             local_download_path
        )
        migrationinput = MigarationInput(
            decoded_email = decoded_email,
            payload = payload,
            organization_name = organization_name,
            user_email = user_email,
            s3_input_path = s3_input_path,
            local_download_path = local_download_path,
            twb_files_count = len(twb_files),
            twb_files = twb_files,
            process_id = process_id
        )
        return MigrationService.execute(MigrationService().tableau_to_powerbi,migrationinput)


    @staticmethod
    async def tableau_to_powerbi(
        workbooks: Optional[List[dict]] = None,
        s3_paths: Optional[List[str]] = None,
    ) -> ServiceResponse:
        """ Migration of tableau files to powerBI """
        s3config = S3Config()
        migration_results = []

        # Process workbooks using hardcoded s3_path structure: BI-PortV3/{organization_name}/{report_id}
        for workbook in workbooks:
            workbook_id = workbook.workbook_id
            workbook_name = workbook.workbook_name
            report = await run_in_threadpool(OldReportDetailsManager.get_workbook_id, workbook_id)
            if not report:
                raise HTTPException(status_code=404, detail=f"Workbook ID '{workbook_id}' not found in report_details")
            custom_zip_name = f"{workbook_name}_pbi_{workbook_id[:8]}.zip"
            if report.is_migrated:
                object_key = MIGRATE_S3_ZIP_PATH.format(WORKBOOKS_PATH = WORKBOOKS_PATH,
                                                        MIGRATE_OUTPUT_DIR = MIGRATE_OUTPUT_DIR,
                                                        workbook_id = workbook_id)
                object_key = os.path.join(os.path.dirname(object_key), custom_zip_name)
                download_link = await s3config.generate_presigned_url(object_key = object_key)
                migration_results.append({
                    "workbook_id": workbook_id,
                    "status": "Success",
                    "data": {"file": custom_zip_name, "download_url": download_link}
                })
            else:
                local_download_path = Path(LOCAL_WORKBOOKS_DOWNLOAD_PATH.format(workbook_id=workbook_id))
                local_download_path.mkdir(parents=True, exist_ok=True)
                s3_workbook_path = S3_WORKBOOKS_PATH.format(workbook_id=workbook_id)
                power_bi_structure = POWER_BI_STRUCTURE.format(workbook_name=workbook_name)
                twb_files = await s3config.download_twb_file_from_s3(
                    s3_workbook_path,
                    str(local_download_path)
                )
                migrationinput = MigarationInput(
                    s3_input_path=s3_workbook_path,
                    local_download_path=str(local_download_path),
                    twb_files_count=len(twb_files),
                    twb_files=twb_files,
                    process_id=workbook_id,
                    powerbi_structure=power_bi_structure
                )
                result = await MigrationService.execute(MigrationService().tableau_to_powerbi, migrationinput, custom_zip_name=custom_zip_name)
                migration_results.append({"workbook_id": workbook_id, "status": "Success", "data": result.data})
                await run_in_threadpool(OldReportDetailsManager.mark_migrated, workbook_id)
        return ServiceResponse(data=migration_results, status_code=HTTP_STATUS_OK, error=None)