from app.services import (add_or_update_userReports,
                          process_files_for_migration)
from ..common import *
from app.schemas.migrate import MigarationInput
from app.core import ServiceResponse,BaseService
from app.services.analysis.s3_service import download_twb_files_from_s3_by_id
from app.core.config import S3Config
from app.core.constants import S3_URL_EXPIRATION_SECONDS
from pathlib import Path
import shutil
import os
from uuid import UUID
from starlette.concurrency import run_in_threadpool
from app.models.report_details import ReportDetailsManager


class MigrationService(BaseService):
    """ Sending Files For Migration """

    @staticmethod
    async def tableau_to_powerbi(request: MigarationInput, custom_zip_name=None) -> ServiceResponse:
        download_links = await process_files_for_migration(
            request.twb_files,
            request.powerbi_structure,
            request.process_id,
            request.s3_input_path,
            custom_zip_name=custom_zip_name
        )
        return ServiceResponse.success(data=download_links, status_code=200)

    async def migrate_tableau_to_powerbi_report(self, report_id: UUID, user):
        """
        Migrates a Tableau report to PowerBI using hardcoded S3 path structure.
        Uses BI-PortV3/{organization_name}/{report_id} as the S3 path.

        Args:
            report_id (UUID): ID of the report to migrate.
            user: Authenticated user.

        Returns:
            dict: Contains download link and report path structure.
        """
        try:
            # Get report details
            report = await run_in_threadpool(ReportDetailsManager.get_report_by_id, report_id)
            if not report:
                raise ValueError(f"Report with ID {report_id} not found")

            report_name = report.name.strip()
            organization_name = user.organization.name

            # Add space for S3 path compatibility (S3 bucket has "Sparity " not "Sparity")
            if organization_name.strip() == "Sparity":
                organization_name = "Sparity "

            # Hardcoded S3 path structure: BI-PortV3/{organization_name}/{report_id}
            s3_base_path = f"BI-PortV3/{organization_name}/{report_id}"

            # Check if already migrated
            s3_config = S3Config()
            custom_zip_name = f"{report_name}_pbi.zip"
            migrated_s3_key = f"{s3_base_path}/migrated_outputs/{custom_zip_name}"

            # Try to get existing migrated file
            try:
                download_url = await s3_config.generate_presigned_url(migrated_s3_key)
                return {
                    "file": custom_zip_name,
                    "download_url": download_url,
                    "report_path": f"BI-PortV3/{organization_name}/{report_id}",
                    "status": "already_migrated"
                }
            except Exception:
                # File doesn't exist, proceed with migration
                pass

            # Download TWB files using the hardcoded S3 path structure
            input_dir, local_dir, unique_folder, _ = await download_twb_files_from_s3_by_id(str(report_id), user)

            # Get TWB files from the input directory
            twb_files = []
            for file_path in Path(input_dir).rglob("*.txt"):
                twb_files.append(str(file_path))

            if not twb_files:
                raise ValueError(f"No TWB files found for report {report_id}")

            # Create migration input
            migrationinput = MigarationInput(
                s3_input_path=f"{s3_base_path}/tableau_file",
                local_download_path=local_dir,
                twb_files_count=len(twb_files),
                twb_files=twb_files,
                process_id=str(report_id),
                powerbi_structure="default_powerbi_structure"
            )

            # Execute migration
            result = await self.tableau_to_powerbi(migrationinput, custom_zip_name=custom_zip_name)

            # Clean up local files
            try:
                shutil.rmtree(local_dir)
            except Exception as e:
                print(f"Failed to delete local files at {local_dir}: {e}")

            return {
                "file": custom_zip_name,
                "download_url": result.data[0] if result.data else None,
                "report_path": unique_folder,
                "status": "migrated"
            }

        except Exception as e:
            raise ValueError(f"Migration failed for report {report_id}: {str(e)}")